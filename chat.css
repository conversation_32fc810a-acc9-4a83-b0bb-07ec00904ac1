/* Agent <PERSON><PERSON>le Chat Window Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #141021;
    color: #F9F9F9;
    height: 100vh;
    overflow: hidden;
    user-select: none;
}

/* Chat Container */
.chat-container {
    width: 100%;
    height: 100vh;
    background: #141021;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Header Styles */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #1D1A2A;
    border-bottom: 2px solid #2C2738;
    -webkit-app-region: drag;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-right {
    display: flex;
    gap: 8px;
    -webkit-app-region: no-drag;
}

/* Logo Styles */
.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.logo-text h1 {
    font-size: 18px;
    font-weight: 700;
    color: #F9F9F9;
    line-height: 1.2;
}

.pro-badge {
    background: linear-gradient(135deg, #FFD84D, #F4BD61);
    color: #2A2C3D;
    padding: 2px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    width: fit-content;
}

/* Window Controls */
.window-control-btn {
    width: 32px;
    height: 32px;
    background: #2C2738;
    border: none;
    border-radius: 8px;
    color: #B2AFC5;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.window-control-btn:hover {
    background: #3D3A4A;
    color: #F9F9F9;
}

.window-control-btn.close-btn:hover {
    background: #FF5C5C;
    color: #F9F9F9;
}

/* Chat Content */
.chat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: #141021;
    scroll-behavior: smooth;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #2C2738;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #5BA9F9;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #4A9AE8;
}

/* Message Bubbles */
.message-bubble {
    max-width: 70%;
    margin-bottom: 16px;
    padding: 16px 20px;
    border-radius: 16px;
    position: relative;
    word-wrap: break-word;
    user-select: text;
}

.message-bubble.user-message {
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    color: #F9F9F9;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message-bubble.ai-message {
    background: #1D1A2A;
    color: #F9F9F9;
    border: 2px solid #2C2738;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

.message-bubble.system-message {
    background: #2C2738;
    color: #B2AFC5;
    margin: 0 auto;
    text-align: center;
    border-radius: 12px;
    max-width: 80%;
}

.message-content h3 {
    color: #F9F9F9;
    margin-bottom: 8px;
    font-size: 16px;
}

.message-content p {
    line-height: 1.5;
    margin-bottom: 8px;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-timestamp {
    font-size: 12px;
    color: #B2AFC5;
    margin-top: 8px;
    opacity: 0.7;
}

/* Welcome Message */
.welcome-message {
    text-align: center;
    margin-bottom: 32px;
}

/* Chat Input */
.chat-input-container {
    background: #1D1A2A;
    border-top: 2px solid #2C2738;
    padding: 20px 24px;
}

.chat-input-wrapper {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

#chatInput {
    flex: 1;
    background: #2C2738;
    border: 2px solid #3D3A4A;
    border-radius: 12px;
    padding: 12px 16px;
    color: #F9F9F9;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.4;
    resize: none;
    transition: border-color 0.2s ease;
    min-height: 44px;
    max-height: 120px;
}

#chatInput:focus {
    outline: none;
    border-color: #5BA9F9;
}

#chatInput::placeholder {
    color: #B2AFC5;
}

.send-btn {
    width: 44px;
    height: 44px;
    background: linear-gradient(135deg, #5BA9F9, #3F2B96);
    border: none;
    border-radius: 12px;
    color: #F9F9F9;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(91, 169, 249, 0.3);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.chat-input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.character-count {
    font-size: 12px;
    color: #B2AFC5;
}

.chat-status {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: #B2AFC5;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background: #4CAF50;
}

.status-indicator.disconnected {
    background: #FF5C5C;
}

.status-indicator.typing {
    background: #FFD84D;
}

/* Loading Indicator */
.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(29, 26, 42, 0.95);
    border: 2px solid #2C2738;
    border-radius: 16px;
    padding: 24px 32px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #2C2738;
    border-top: 3px solid #5BA9F9;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-indicator span {
    color: #B2AFC5;
    font-size: 14px;
    font-weight: 500;
}

/* Animations */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 600px) {
    .chat-header {
        padding: 12px 16px;
    }
    
    .chat-messages {
        padding: 16px;
    }
    
    .chat-input-container {
        padding: 16px;
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .logo-text h1 {
        font-size: 16px;
    }
}

/* Message Animation */
.message-bubble {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    margin-bottom: 16px;
    background: #1D1A2A;
    border: 2px solid #2C2738;
    border-radius: 16px;
    border-bottom-left-radius: 4px;
    max-width: 100px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #5BA9F9;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes typingBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}