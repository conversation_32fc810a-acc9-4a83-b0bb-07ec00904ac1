<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Agent <PERSON><PERSON><PERSON> Chat - mac<PERSON></title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
  <style>
    body { font-family: 'Inter', sans-serif; }
    .typing-indicator span {
      animation: blink 1.4s infinite both;
    }
    .typing-indicator span:nth-child(2) { animation-delay: 0.2s; }
    .typing-indicator span:nth-child(3) { animation-delay: 0.4s; }
    @keyframes blink {
      0% { opacity: 0.1; }
      20% { opacity: 1; }
      100% { opacity: 0.1; }
    }
  </style>
</head>
<body class="bg-gray-100 h-screen p-8 flex items-center justify-center">
  <div class="w-full max-w-6xl h-[700px] rounded-lg bg-[#1E1E1E] shadow-2xl overflow-hidden border border-gray-700 relative">
    <!-- Window Controls Bar -->
    <div class="h-6 bg-[#2A2A2A] flex items-center px-3">
      <div class="flex items-center space-x-1.5">
        <div class="w-3 h-3 rounded-full bg-red-500"></div>
        <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
        <div class="w-3 h-3 rounded-full bg-green-500"></div>
      </div>
    </div>

    <!-- App Content -->
    <div class="flex h-[calc(100%-1.5rem)]">
      <!-- Sidebar -->
      <div class="w-64 bg-gray-800 flex flex-col border-r border-gray-700">
        <!-- New Chat Button -->
        <div class="p-4">
          <button id="new-chat-btn" class="w-full flex items-center justify-between rounded-md border border-gray-600 px-3 py-2 text-sm font-medium hover:bg-gray-700 text-white">
            <span class="flex items-center">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5v15m7.5-7.5h-15"></path></svg>
              New chat
            </span>
          </button>
        </div>

        <!-- Recent Conversations -->
        <div class="flex-1 overflow-y-auto">
          <div class="px-3 py-2">
            <h3 class="text-xs text-gray-400 font-medium mb-2">Today</h3>
            <div id="conversation-list" class="space-y-1">
              <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center justify-between group text-white">
                <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  Explaining quantum computing
                </div>
              </button>
              <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center justify-between group text-gray-300">
                <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                  Creative writing prompts
                </div>
              </button>
            </div>
          </div>
        </div>

        <!-- User Profile -->
        <div class="border-t border-gray-700 pt-2 pb-4">
          <div class="px-3 py-2">
            <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center text-gray-300">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg"><path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Zm0 0a9 9 0 0 0 5.636-1.968m-11.272 0A9 9 0 0 0 12 21Z"></path><circle cx="12" cy="9" r="3"></circle></svg>
              Upgrade to Plus
            </button>
            <button class="w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center justify-between text-white">
              <div class="flex items-center">
                <div class="h-7 w-7 rounded-full bg-blue-600 flex items-center justify-center mr-2">
                  <span class="text-xs font-medium">AH</span>
                </div>
                <span>Agent Hustle</span>
              </div>
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-4 w-4" xmlns="http://www.w3.org/2000/svg"><path d="M12 12h.01M12 6h.01M12 18h.01"></path></svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Main Chat Area -->
      <div class="flex-1 flex flex-col overflow-hidden relative">
        <!-- Chat Header -->
        <div class="border-b border-gray-700 p-3 flex items-center justify-between bg-gray-800">
          <div class="flex items-center">
            <span class="font-medium text-white">Agent Hustle</span>
            <span class="ml-2 px-2 py-1 rounded-md bg-blue-600 text-xs text-white">PRO</span>
          </div>
          <div class="flex items-center space-x-2">
            <button class="p-1.5 rounded-md hover:bg-gray-700 text-gray-300">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"><path d="M12 5v14m-7-7h14"></path></svg>
            </button>
            <button class="p-1.5 rounded-md hover:bg-gray-700 text-gray-300">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg"><path d="M12 21a9 9 0 1 0 0-18 9 9 0 0 0 0 18Z"></path><path d="M12 12h.01M12 8h.01M12 16h.01"></path></svg>
            </button>
          </div>
        </div>

        <!-- Chat Messages -->
        <div id="chat-messages" class="flex-1 overflow-y-auto p-4 space-y-6 bg-gray-900">
          <!-- System Welcome Message -->
          <div class="flex items-start">
            <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
              <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
              </svg>
            </div>
            <div class="flex-1">
              <div class="text-sm text-gray-200">
                <p class="mb-2"><strong>Agent Hustle</strong></p>
                <p>Hello! I'm Agent Hustle, an AI assistant created by HustlePlug. I'm here to provide information, answer questions, assist with tasks, and engage in conversations on a wide range of topics. How can I help you today?</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Input Area -->
        <div class="p-4 border-t border-gray-700 bg-gray-800">
          <div class="relative rounded-lg border border-gray-600 bg-gray-700 shadow-sm">
            <textarea id="user-input" class="w-full p-3 pr-12 text-sm bg-transparent focus:outline-none resize-none text-white" rows="1" placeholder="Message Agent Hustle..."></textarea>
            <button id="send-button" class="absolute right-2 bottom-2 p-1 rounded-md text-gray-400 hover:bg-gray-600">
              <svg stroke="currentColor" fill="none" stroke-width="2" viewBox="0 0 24 24" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 12L3.269 3.126A59.768 59.768 0 0721.485 12 59.77 59.77 0 713.27 20.876L5.999 12zm0 0h7.5"></path>
              </svg>
            </button>
          </div>
          <div class="mt-2 text-xs text-center text-gray-400">
            Agent Hustle can make mistakes. Consider checking important information.
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Elements
    const userInput = document.getElementById('user-input');
    const sendButton = document.getElementById('send-button');
    const chatMessages = document.getElementById('chat-messages');
    const newChatButton = document.getElementById('new-chat-btn');
    const conversationList = document.getElementById('conversation-list');

    // Sample responses for demonstration
    const sampleResponses = [
      "I'd be happy to help with that! Let me think about this for a moment...",
      "That's an interesting question. From my understanding, there are several factors to consider...",
      "Based on the information available to me, I can tell you that this topic has multiple perspectives...",
      "Great question! Here's what I know about this subject...",
      "I'd like to provide some context before answering your question directly..."
    ];

    // More detailed responses for specific keywords
    const keywordResponses = {
      "hello": "Hello! How can I assist you today?",
      "help": "I'm here to help! Please let me know what you need assistance with, and I'll do my best to provide information or guidance.",
      "thanks": "You're welcome! If you have any other questions or need further assistance, feel free to ask.",
      "weather": "I don't have real-time access to weather data, but I can help you understand weather patterns or direct you to reliable weather services.",
      "javascript": "JavaScript is a programming language commonly used to create interactive effects within web browsers. What specific aspect of JavaScript would you like to know about?",
      "python": "Python is a high-level, interpreted programming language known for its readability and versatility. It's widely used in data science, web development, automation, and many other fields. What would you like to know about Python?"
    };

    // Function to add user message to chat
    function addUserMessage(message) {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'flex items-start';
      messageDiv.innerHTML = `
        <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
          <span class="text-xs font-medium text-white">AH</span>
        </div>
        <div class="flex-1">
          <div class="text-sm text-gray-200">
            <p class="mb-2"><strong>You</strong></p>
            <p>${message}</p>
          </div>
        </div>
      `;
      chatMessages.appendChild(messageDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;

      // Add to conversation list
      addToConversationList(message);
    }

    // Function to add AI response to chat
    function addAIResponse(message) {
      const messageDiv = document.createElement('div');
      messageDiv.className = 'flex items-start';
      messageDiv.innerHTML = `
        <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
          <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
          </svg>
        </div>
        <div class="flex-1">
          <div class="text-sm text-gray-200">
            <p class="mb-2"><strong>Agent Hustle</strong></p>
            <p>${message}</p>
          </div>
        </div>
      `;
      chatMessages.appendChild(messageDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to add typing indicator
    function showTypingIndicator() {
      const indicatorDiv = document.createElement('div');
      indicatorDiv.className = 'flex items-start typing-container';
      indicatorDiv.id = 'typing-indicator';
      indicatorDiv.innerHTML = `
        <div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
          <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
          </svg>
        </div>
        <div class="flex-1">
          <div class="text-sm text-gray-200">
            <p class="mb-2"><strong>Agent Hustle</strong></p>
            <p class="typing-indicator">
              <span>•</span>
              <span>•</span>
              <span>•</span>
            </p>
          </div>
        </div>
      `;
      chatMessages.appendChild(indicatorDiv);
      chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to remove typing indicator
    function removeTypingIndicator() {
      const indicator = document.getElementById('typing-indicator');
      if (indicator) {
        indicator.remove();
      }
    }

    // Function to get response based on user input
    function getAIResponse(userMessage) {
      const lowercaseMessage = userMessage.toLowerCase();

      // Check for keyword matches first
      for (const [keyword, response] of Object.entries(keywordResponses)) {
        if (lowercaseMessage.includes(keyword)) {
          return response;
        }
      }

      // If no keyword match, return a random response
      return sampleResponses[Math.floor(Math.random() * sampleResponses.length)];
    }

    // Function to add conversation to the sidebar list
    function addToConversationList(message) {
      // Truncate message if too long
      const truncatedMessage = message.length > 30 ? message.substring(0, 27) + '...' : message;

      const conversationButton = document.createElement('button');
      conversationButton.className = 'w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center justify-between group text-gray-300';
      conversationButton.innerHTML = `
        <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
          ${truncatedMessage}
        </div>
      `;

      // Insert at the top of the list
      if (conversationList.firstChild) {
        conversationList.insertBefore(conversationButton, conversationList.firstChild);
      } else {
        conversationList.appendChild(conversationButton);
      }
    }

    // Function to handle sending a message
    function sendMessage() {
      const message = userInput.value.trim();
      if (message) {
        // Add user message to chat
        addUserMessage(message);
        userInput.value = '';

        // Show typing indicator
        showTypingIndicator();

        // Simulate AI thinking and responding
        setTimeout(() => {
          removeTypingIndicator();
          const response = getAIResponse(message);
          addAIResponse(response);
        }, 1500);
      }
    }

    // Function to clear chat
    function clearChat() {
      while (chatMessages.children.length > 1) {
        chatMessages.removeChild(chatMessages.lastChild);
      }
    }

    // Event Listeners
    sendButton.addEventListener('click', sendMessage);

    userInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
      }
    });

    newChatButton.addEventListener('click', clearChat);

    // Auto-resize textarea as user types
    userInput.addEventListener('input', function() {
      this.style.height = 'auto';
      this.style.height = (this.scrollHeight) + 'px';
    });
  </script>
</body>
</html>