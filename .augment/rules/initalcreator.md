---
type: "agent_requested"
description: "Enter I mode"
---
I want you to enter context mode and gather comprehensive context for [DESCRIBE SPECIFIC ISSUES/TASKS]. 

**CONTEXT GATHERING PHASE - Complete these steps systematically:**

1. **🔄 Index Refresh & Setup**
   - First, refresh the code index using MCP tools to ensure accurate context
   - Set the project path and verify file accessibility
   - Confirm search capabilities are enabled

2. **🔍 Architecture Discovery**
   - Search the codebase to understand the overall architecture pattern
   - Identify main technologies, frameworks, and design patterns used
   - Look for existing file organization and naming conventions
   - Find any architectural documentation or configuration files

3. **📁 Structure Analysis** 
   - Examine current folder structure and file organization
   - Identify where similar features/components are implemented
   - Look for size limits, coding standards, or style guides
   - Check for existing manager/service/component patterns

4. **🧩 Integration Points**
   - Find how existing features are integrated into the main application
   - Look for navigation patterns, routing, or section management
   - Identify event handling and state management approaches
   - Check API integration patterns and data flow

5. **🎨 UI/UX Patterns**
   - Examine existing UI components and styling approaches
   - Look for design system, CSS organization, or component libraries
   - Check responsive design patterns and accessibility considerations
   - Find modal, form, or interaction patterns relevant to the issues

6. **🔐 Security & Constraints**
   - Identify authentication/authorization patterns
   - Look for data storage and privacy considerations
   - Find validation, security measures, or rate limiting
   - Check for tier-based features (free vs premium)

**ISSUE-SPECIFIC INVESTIGATION:**

Key issues to investigate:
- [ISSUE 1: Brief description]
- [ISSUE 2: Brief description] 
- [ISSUE 3: Brief description]
- [Additional issues as needed]

For each issue:
- Use search_code_advanced_code-index to find relevant code patterns
- Identify specific files, line numbers, and current implementations
- Document existing patterns that can be followed for fixes
- Note any missing implementations or broken connections

**TEMPLATE REFERENCE & FINAL DOCUMENTATION:**

After gathering all context:

1. **Read the existing INITIAL.md file** at `[PROJECT_PATH]/context-engineering-intro-main/INITIAL.md` to understand the documentation format, structure, and style that should be followed

2. **Create a new INITIAL.md file** at `[PROJECT_PATH]/context-engineering-intro-main/INITIAL-[UNIQUE-DESCRIPTIVE-NAME].md` that follows the same format and structure as the template, but contains all your gathered context about the current issues

The new INITIAL.md should:
- Follow the exact same format/structure as the template INITIAL.md
- Include all the context you gathered from your code analysis
- Contain specific issues and their solutions
- Provide implementation roadmap
- Include ready-to-execute code snippets
- Match the documentation style and organization of the template

This ensures consistency in documentation format while providing comprehensive context for the specific tasks at hand.

**CRITICAL REQUIREMENTS:**
- Use search_code_advanced_code-index as your first action before any analysis
- Read the existing INITIAL.md file as a template/example to follow
- Create a new INITIAL.md file that matches the template's format but contains your analysis
- Follow ALL existing architectural patterns and constraints
- Provide exact file locations and line numbers for all issues
- Include ready-to-execute code snippets
- Ensure the final INITIAL.md follows the established documentation pattern