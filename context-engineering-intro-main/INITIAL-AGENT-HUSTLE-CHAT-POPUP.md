## FEATURE:

Agent <PERSON>le Chat Pop-out Integration for HustlePlug Chrome Extension

Integrate the Agent Hustle chat interface as a pop-out chat experience that opens in its own separate window when activated from the Quick Actions page. The chat UI must reflect HustlePlug branding while following existing design patterns and providing a complete, functional, and polished user experience.

## EXAMPLES:

### Current Architecture Patterns Found:
1. **Manager-Based Architecture**: All features use BaseManager pattern extending from `js/popup/core/BaseManager.js`
2. **Event Management**: Centralized event handling via `js/popup/ui/EventManager.js` with `addEventListenerTracked()` method
3. **Navigation System**: Section-based navigation using `navigateToSection()` and `goBack()` methods in PopupController
4. **Modal Patterns**: Existing modal system in `styles/components/_modal.css` with backdrop blur and slide-in animations
5. **Button Patterns**: Action buttons in Quick Actions use `.action-btn` class with icon + text layout

### Existing Quick Actions Structure (popup.html lines 50-144):
```html
<div id="actionsSection" class="section">
    <div class="action-grid">
        <button id="analyzeSelection" class="action-btn">
            <div class="action-icon analyze-selection">
                <svg>...</svg>
            </div>
            <div class="action-text">
                <div class="action-title">Analyze Selection</div>
                <div class="action-desc">Analyze selected text</div>
            </div>
        </button>
        <!-- More action buttons... -->
    </div>
</div>
```

### Event Handler Pattern (EventManager.js lines 60-67):
```javascript
this.addEventListenerTracked('analyzeSelection', 'click', () => {
    this.controller.analysisManager.analyzeSelection();
});
```

### Branding Elements Found:
- **Colors**: Primary gradient `#5BA9F9 to #3F2B96`, secondary `#2C2738`
- **Logo**: Rocket SVG with gradient `#FF5C5C to #FFD84D to #5BA9F9`
- **Typography**: Inter font family, professional styling
- **Pro Badge**: Orange gradient badge for premium features

## DOCUMENTATION:

### Key Files to Reference:
1. **popup.html** (lines 50-144): Quick Actions section structure and button patterns
2. **js/popup/ui/EventManager.js** (lines 43-86): Event handling patterns for action buttons
3. **js/popup/core/PopupController.js** (lines 145-190): Navigation and section management
4. **styles/components/_modal.css**: Modal styling patterns with backdrop and animations
5. **styles/components/_buttons.css** (lines 95-150): Action button styling patterns
6. **js/popup/core/BaseManager.js**: Base class for all managers
7. **manifest.json**: Chrome extension permissions and configuration

### Agent Hustle Chat Reference:
- **Chat HTML Template**: Provided ChatGPT-style interface with sidebar, message area, and input
- **Styling**: Dark theme with macOS-style window controls
- **Features**: Message history, typing indicators, user avatars, responsive design

### Chrome Extension APIs Needed:
- `chrome.windows.create()`: For creating pop-out windows
- `chrome.runtime.getURL()`: For loading chat HTML file
- Window communication via `postMessage()` for data exchange

### Integration Points:
1. **Quick Actions Button**: Add new "Agent Hustle Chat" button to action grid
2. **Event Handler**: Create click handler to open pop-out window
3. **Chat Manager**: New manager class to handle chat window lifecycle
4. **Styling**: Adapt chat interface to match HustlePlug branding
5. **Communication**: Establish message passing between popup and chat window

## OTHER CONSIDERATIONS:

### Critical Implementation Requirements:
1. **Follow Existing Patterns**: Must use BaseManager architecture and EventManager patterns
2. **Branding Consistency**: Chat interface must use HustlePlug colors, fonts, and styling
3. **Complete Functionality**: All chat features must work (send, receive, history, close)
4. **Window Management**: Proper handling of window creation, focus, and cleanup
5. **Error Handling**: Graceful fallbacks if window creation fails

### Technical Gotchas:
1. **Chrome Extension Context**: Pop-out windows run in different context than popup
2. **Message Passing**: Need proper communication channel between popup and chat window
3. **Resource Loading**: Chat HTML/CSS/JS must be properly bundled and accessible
4. **Window Lifecycle**: Handle window close events and cleanup
5. **Permissions**: Ensure manifest.json has required permissions for window creation

### UX Flow Requirements:
1. **Click-to-Open**: Single click on "Agent Hustle Chat" button opens chat window
2. **Window Sizing**: Chat opens in appropriately sized window (800x600 recommended)
3. **Close Button**: Prominent X button in chat window for easy closing
4. **Focus Management**: Chat window should gain focus when opened
5. **Prevent Duplicates**: Don't open multiple chat windows simultaneously

### Branding Adaptation Needed:
1. **Color Scheme**: Replace ChatGPT green with HustlePlug blue gradient
2. **Logo/Branding**: Replace ChatGPT branding with Agent Hustle rocket logo
3. **Typography**: Use Inter font family to match extension
4. **Window Title**: "Agent Hustle Chat" instead of "ChatGPT"
5. **User Avatar**: Use HustlePlug styling for user representation

### File Structure to Create:
1. **chat.html**: Main chat interface HTML file
2. **chat.css**: Chat-specific styling (adapted from provided template)
3. **chat.js**: Chat functionality and communication logic
4. **js/popup/chat/ChatManager.js**: Manager class for chat window handling
5. **Update EventManager.js**: Add chat button event handler
6. **Update popup.html**: Add chat button to Quick Actions grid

### Implementation Steps:
1. Create chat.html with HustlePlug branding
2. Create chat.css with adapted styling
3. Create chat.js with communication logic
4. Create ChatManager.js following BaseManager pattern
5. Add chat button to Quick Actions grid
6. Add event handler in EventManager.js
7. Update manifest.json permissions if needed

### Security Considerations:
1. **Content Security Policy**: Ensure chat resources comply with extension CSP
2. **Message Validation**: Validate all messages between popup and chat window
3. **Resource Access**: Properly scope access to extension resources
4. **User Data**: Handle chat history and user data securely
