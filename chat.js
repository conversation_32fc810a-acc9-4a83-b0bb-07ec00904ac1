/**
 * Agent <PERSON><PERSON><PERSON>t <PERSON>
 * Handles chat functionality and communication with the popup window
 */

class ChatWindow {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendButton = document.getElementById('sendMessage');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.chatStatus = document.getElementById('chatStatus');
        this.characterCount = document.querySelector('.character-count');
        this.conversationList = document.getElementById('conversation-list');
        this.newChatBtn = document.getElementById('new-chat-btn');
        
        this.isConnected = false;
        this.isTyping = false;
        this.messageHistory = [];
        this.conversations = [];
        this.currentConversationId = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMessageHandlers();
        this.updateStatus('ready', 'Ready');
        this.notifyParentReady();
        
        // Focus on input when window opens
        setTimeout(() => {
            this.chatInput.focus();
        }, 100);
    }

    setupEventListeners() {
        // Send message button
        this.sendButton.addEventListener('click', () => {
            this.handleSendMessage();
        });

        // Enter key to send message
        this.chatInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.handleSendMessage();
            }
        });

        // Input changes
        this.chatInput.addEventListener('input', () => {
            this.handleInputChange();
            this.autoResizeTextarea();
        });

        // Window close button
        document.getElementById('closeChatWindow').addEventListener('click', () => {
            this.closeWindow();
        });

        // Minimize button
        document.getElementById('minimizeChat').addEventListener('click', () => {
            this.minimizeWindow();
        });

        // Handle window closing
        window.addEventListener('beforeunload', () => {
            this.notifyParentClosing();
        });

        // New chat button
        this.newChatBtn.addEventListener('click', () => {
            this.startNewChat();
        });
    }

    setupMessageHandlers() {
        // Listen for messages from parent popup
        window.addEventListener('message', (event) => {
            this.handleParentMessage(event);
        });

        // Listen for chrome runtime messages
        if (chrome && chrome.runtime && chrome.runtime.onMessage) {
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                switch (request.type) {
                    case 'AI_RESPONSE':
                        this.handleAIResponse(request.data);
                        break;
                    case 'AI_ERROR':
                        this.handleAIError(request.data);
                        break;
                    case 'CHAT_MESSAGE':
                        this.handleIncomingMessage(request.data);
                        break;
                }
                sendResponse({ success: true });
                return true;
            });
        }
    }

    handleInputChange() {
        const text = this.chatInput.value;
        const length = text.length;
        const maxLength = 2000;
        
        // Update character count
        this.characterCount.textContent = `${length}/${maxLength}`;
        
        // Enable/disable send button
        this.sendButton.disabled = length === 0 || length > maxLength;
        
        // Update character count color
        if (length > maxLength * 0.9) {
            this.characterCount.style.color = '#FF5C5C';
        } else if (length > maxLength * 0.8) {
            this.characterCount.style.color = '#FFD84D';
        } else {
            this.characterCount.style.color = '#9CA3AF';
        }
    }

    autoResizeTextarea() {
        this.chatInput.style.height = 'auto';
        this.chatInput.style.height = (this.chatInput.scrollHeight) + 'px';
    }

    handleSendMessage() {
        const message = this.chatInput.value.trim();
        if (!message || message.length > 2000) return;

        // Add user message to chat
        this.addMessage('user', message);
        
        // Clear input
        this.chatInput.value = '';
        this.handleInputChange();
        
        // Show loading state
        this.showLoading();
        this.updateStatus('typing', 'AI is thinking...');
        
        // Store message in history
        this.messageHistory.push({ role: 'user', content: message, timestamp: Date.now() });
        
        // Send message to AI via popup
        this.sendMessageToAI(message);
        
        // Focus back on input
        this.chatInput.focus();
    }

    /**
     * Send message to AI via popup ChatManager
     */
    async sendMessageToAI(message) {
        try {
            // Send message to popup window to handle AI communication
            if (window.opener && !window.opener.closed) {
                window.opener.postMessage({
                    type: 'CHAT_MESSAGE',
                    source: 'agent-hustle-chat',
                    data: message
                }, '*');
            } else {
                throw new Error('Connection to popup lost');
            }
        } catch (error) {
            console.error('Failed to send message to AI:', error);
            this.hideLoading();
            this.updateStatus('disconnected', 'Connection lost');
            this.addMessage('system', 'Connection to AI service lost. Please close and reopen the chat window.');
        }
    }

    addMessage(type, content, timestamp = null) {
        // Render the message
        this.renderMessage(type, content, timestamp);
        
        // Store in history
        const messageObj = { 
            role: type, 
            content: content, 
            timestamp: timestamp || Date.now() 
        };
        this.messageHistory.push(messageObj);
        
        // Update conversation history if this is the first message of a new conversation
        if (this.currentConversationId === null && type === 'user') {
            this.startNewConversation(content);
        }
        
        // Update current conversation with the new message
        if (this.currentConversationId) {
            const conversation = this.conversations.find(c => c.id === this.currentConversationId);
            if (conversation) {
                conversation.messages.push(messageObj);
            }
        }
    }

    renderMessage(type, content, timestamp = null) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start';
        
        let avatarHtml = '';
        let senderName = '';
        
        if (type === 'user') {
            avatarHtml = `<div class="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                <span class="text-xs font-medium text-white">U</span>
            </div>`;
            senderName = 'You';
        } else if (type === 'ai') {
            avatarHtml = `<div class="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 flex items-center justify-center flex-shrink-0 mr-3">
                <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
                </svg>
            </div>`;
            senderName = 'Agent Hustle';
        } else if (type === 'system') {
            avatarHtml = `<div class="h-8 w-8 rounded-full bg-gray-600 flex items-center justify-center flex-shrink-0 mr-3">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2C6.477 2 2 6.477 2 12s4.477 10 10 10 10-4.477 10-10S17.523 2 12 2zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8zm-1-13v4h-3v2h3v4h2v-4h3v-2h-3V7h-2z"></path>
                </svg>
            </div>`;
            senderName = 'System';
        }
        
        // Format content based on type
        let formattedContent = '';
        if (type === 'user') {
            formattedContent = this.escapeHtml(content);
        } else if (type === 'ai') {
            formattedContent = this.formatAIResponse(content);
        } else if (type === 'system') {
            formattedContent = content;
        }
        
        messageDiv.innerHTML = `
            ${avatarHtml}
            <div class="flex-1">
                <div class="text-sm text-gray-200">
                    <p class="mb-2"><strong>${senderName}</strong></p>
                    <p>${formattedContent}</p>
                    ${timestamp ? `<p class="text-xs text-gray-400 mt-2">${new Date(timestamp).toLocaleTimeString()}</p>` : ''}
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    handleAIResponse(response) {
        this.hideLoading();
        this.updateStatus('ready', 'Ready');
        
        // Extract content from response
        let content = '';
        if (typeof response === 'string') {
            content = response;
        } else if (response.content) {
            content = response.content;
        } else if (response.data && response.data.content) {
            content = response.data.content;
        } else {
            content = 'I received your message but had trouble processing it. Please try again.';
        }
        
        // Add AI response to chat
        this.addMessage('ai', content, Date.now());
        
        // Store in history
        this.messageHistory.push({ 
            role: 'ai', 
            content: content, 
            timestamp: Date.now() 
        });
    }

    handleAIError(error) {
        this.hideLoading();
        this.updateStatus('disconnected', 'Error occurred');
        
        // Add error message to chat
        this.addMessage('system', `AI service error: ${error}. Please try again or check your connection.`);
    }

    formatAIResponse(content) {
        // Basic markdown-style formatting
        let formatted = this.escapeHtml(content);
        
        // Bold text
        formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
        
        // Italic text
        formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
        
        // Code blocks
        formatted = formatted.replace(/`(.*?)`/g, '<code>$1</code>');
        
        // Line breaks
        formatted = formatted.replace(/\n/g, '<br>');
        
        return formatted;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoading() {
        // Show typing indicator in chat messages
        this.addTypingIndicator();
        this.loadingIndicator.style.display = 'flex';
        this.isTyping = true;
    }

    hideLoading() {
        // Remove typing indicator from chat messages
        this.removeTypingIndicator();
        this.loadingIndicator.style.display = 'none';
        this.isTyping = false;
    }

    addTypingIndicator() {
        // Remove any existing typing indicator
        this.removeTypingIndicator();
        
        const typingDiv = document.createElement('div');
        typingDiv.className = 'flex items-start typing-container';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 flex items-center justify-center flex-shrink-0 mr-3">
                <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
                </svg>
            </div>
            <div class="flex-1">
                <div class="text-sm text-gray-200">
                    <p class="mb-2"><strong>Agent Hustle</strong></p>
                    <p class="typing-indicator">
                        <span>•</span>
                        <span>•</span>
                        <span>•</span>
                    </p>
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    removeTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    updateStatus(type, message) {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = document.querySelector('.status-text');
        
        // Remove existing status classes
        statusIndicator.classList.remove('connected', 'disconnected', 'typing');
        
        // Add new status class
        statusIndicator.classList.add(type);
        statusText.textContent = message;
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }

    // Communication with parent popup
    notifyParentReady() {
        try {
            if (window.opener && !window.opener.closed) {
                window.opener.postMessage({
                    type: 'CHAT_READY',
                    source: 'agent-hustle-chat'
                }, '*');
            }
        } catch (error) {
            console.log('Could not notify parent window:', error);
        }
    }

    notifyParentClosing() {
        try {
            if (window.opener && !window.opener.closed) {
                window.opener.postMessage({
                    type: 'WINDOW_CLOSING',
                    source: 'agent-hustle-chat'
                }, '*');
            }
        } catch (error) {
            console.log('Could not notify parent window:', error);
        }
    }

    handleParentMessage(event) {
        // Validate message origin for security
        if (!event.data || event.data.source !== 'agent-hustle-popup') {
            return;
        }

        switch (event.data.type) {
            case 'SEND_MESSAGE':
                this.handleIncomingMessage(event.data.message);
                break;
            case 'CHAT_CONFIG':
                this.handleConfigUpdate(event.data.config);
                break;
            default:
                console.warn('Unknown message type from parent:', event.data.type);
        }
    }

    handleIncomingMessage(message) {
        this.addMessage('ai', message, Date.now());
    }

    handleConfigUpdate(config) {
        // Handle configuration updates from parent
        if (config.theme) {
            document.body.setAttribute('data-theme', config.theme);
        }
    }

    // Window controls
    closeWindow() {
        this.notifyParentClosing();
        window.close();
    }

    minimizeWindow() {
        // Chrome extensions can't minimize windows, so we'll just hide it
        if (window.chrome && window.chrome.windows) {
            chrome.windows.update(window.chrome.windows.WINDOW_ID_CURRENT, {
                state: 'minimized'
            });
        }
    }

    // Export chat history
    exportHistory() {
        const history = {
            messages: this.messageHistory,
            timestamp: new Date().toISOString(),
            windowId: 'agent-hustle-chat'
        };
        
        const dataStr = JSON.stringify(history, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `agent-hustle-chat-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Clear chat history
    clearHistory() {
        if (confirm('Are you sure you want to clear the chat history?')) {
            this.messageHistory = [];
            this.chatMessages.innerHTML = `
                <div class="flex items-start">
                    <div class="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 flex items-center justify-center flex-shrink-0 mr-3">
                        <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <div class="text-sm text-gray-200">
                            <p class="mb-2"><strong>Agent Hustle</strong></p>
                            <p>Welcome to Agent Hustle Chat! I'm here to provide information, answer questions, assist with tasks, and engage in conversations on a wide range of topics. How can I help you today?</p>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    // Start a new conversation
    startNewChat() {
        this.currentConversationId = null;
        this.messageHistory = [];
        this.clearHistory();
    }

    // Start a new conversation with the first message
    startNewConversation(firstMessage) {
        const conversationId = 'conv_' + Date.now();
        this.currentConversationId = conversationId;
        
        // Create conversation preview (truncate if too long)
        const preview = firstMessage.length > 30 ? firstMessage.substring(0, 27) + '...' : firstMessage;
        
        // Add to conversations list
        this.conversations.unshift({
            id: conversationId,
            preview: preview,
            timestamp: Date.now(),
            messages: []
        });
        
        // Update sidebar
        this.updateConversationList();
    }

    // Update conversation list in sidebar
    updateConversationList() {
        this.conversationList.innerHTML = '';
        
        this.conversations.forEach(conversation => {
            const conversationButton = document.createElement('button');
            conversationButton.className = 'w-full text-left rounded-md px-3 py-2 text-sm hover:bg-gray-700 flex items-center justify-between group text-gray-300';
            
            if (conversation.id === this.currentConversationId) {
                conversationButton.classList.add('bg-gray-700', 'text-white');
            }
            
            conversationButton.innerHTML = `
                <div class="flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                    ${conversation.preview}
                </div>
            `;
            
            conversationButton.addEventListener('click', () => {
                this.loadConversation(conversation.id);
            });
            
            this.conversationList.appendChild(conversationButton);
        });
    }

    // Load a specific conversation
    loadConversation(conversationId) {
        const conversation = this.conversations.find(c => c.id === conversationId);
        if (!conversation) return;
        
        this.currentConversationId = conversationId;
        this.messageHistory = conversation.messages;
        
        // Clear current messages and reload
        this.chatMessages.innerHTML = '';
        
        // Add welcome message
        this.chatMessages.innerHTML = `
            <div class="flex items-start">
                <div class="h-8 w-8 rounded-full bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 flex items-center justify-center flex-shrink-0 mr-3">
                    <svg width="16" height="16" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20 2L24 12L34 8L28 18L38 20L28 22L34 32L24 28L20 38L16 28L6 32L12 22L2 20L12 18L6 8L16 12L20 2Z" fill="white"/>
                    </svg>
                </div>
                <div class="flex-1">
                    <div class="text-sm text-gray-200">
                        <p class="mb-2"><strong>Agent Hustle</strong></p>
                        <p>Welcome to Agent Hustle Chat! I'm here to provide information, answer questions, assist with tasks, and engage in conversations on a wide range of topics. How can I help you today?</p>
                    </div>
                </div>
            </div>
        `;
        
        // Reload messages without triggering side effects
        this.messageHistory.forEach(message => {
            this.renderMessage(message.role, message.content, message.timestamp);
        });
        
        // Update sidebar
        this.updateConversationList();
    }
}

// Initialize chat window when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.chatWindow = new ChatWindow();
});

// Global error handling
window.addEventListener('error', (event) => {
    console.error('Chat window error:', event.error);
});

// Handle unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection in chat window:', event.reason);
});