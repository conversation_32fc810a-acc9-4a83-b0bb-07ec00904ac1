# CODE MODIFICATION ENFORCEMENT RULES

## ⚠️ **MANDATORY ENFORCEMENT:**

For *every* user request that involves writing, modifying, or debugging code in the HustlePlug project, the assistant **must** follow this exact workflow:

### **Required Actions Before Code Changes (IN ORDER):**

1. **🔍 GATHER CONTEXT FIRST**: Use regular tools (`codebase-retrieval`, `view`) for initial understanding
2. **🛠️ SET PROJECT PATH**: Use `set_project_path_code-index` to initialize MCP tools
3. **🔎 DEEP CODE SEARCH**: Use `search_code_advanced_code-index` to find relevant patterns
4. **📊 ANALYZE PATTERNS**: Understand existing implementation patterns  
5. **🎯 IDENTIFY FILES**: Locate all files that may be affected
6. **✅ VERIFY CONTEXT**: Ensure understanding of codebase structure

### **You may only produce or edit code *after* these tool calls and their successful results.**

## 📋 **Specific Search Requirements:**

### **For Bug Fixes:**
- Search for the error/issue pattern
- Search for related functionality 
- Search for similar implementations
- Example: `search_code_advanced_code-index "loadChatHistory"`

### **For New Features:**
- Search for similar existing features
- Search for integration patterns
- Search for storage/API patterns
- Example: `search_code_advanced_code-index "BaseManager"`

### **For Refactoring:**
- Search for all instances of code being changed
- Search for dependent code
- Search for similar patterns
- Example: `search_code_advanced_code-index "chrome.storage.local"`

## 🚫 **VIOLATIONS:**

The following actions are **PROHIBITED** without prior workflow completion:

- ❌ Writing new JavaScript files
- ❌ Modifying existing .js files
- ❌ Adding HTML sections
- ❌ Changing CSS styles
- ❌ Updating configuration files
- ❌ Modifying storage patterns
- ❌ Adding new API calls
- ❌ Changing manager integrations

## ✅ **EXCEPTIONS:**

The following actions may proceed without MCP workflow:

- ✅ Reading/viewing existing files
- ✅ Creating documentation files
- ✅ Writing test plans
- ✅ Creating markdown files
- ✅ Explaining existing code

## 🎯 **ENFORCEMENT WORKFLOW:**

```
User Request → Context Gathering → Set MCP Path → Deep Code Search → Pattern Analysis → Code Modification
     ↓              ↓                   ↓              ↓               ↓              ↓
   Identify    Use codebase-        set_project_   search_code_    Understand     Follow Existing
   Intent      retrieval/view       path_code-     advanced_       Architecture    Patterns
                                   index          code-index
```

## 📝 **COMPLIANCE EXAMPLES:**

### ✅ **CORRECT Approach:**
```
User: "Fix the chat history saving issue"
Assistant: 
1. codebase-retrieval "chat history saving functionality"
2. view relevant files
3. set_project_path_code-index "/path/to/project"
4. search_code_advanced_code-index "loadChatHistory"
5. search_code_advanced_code-index "saveAnalysis" 
6. search_code_advanced_code-index "chrome.storage.local"
7. Analyze patterns and create fix
```

### ❌ **INCORRECT Approach:**
```
User: "Fix the chat history saving issue"
Assistant: 
1. Immediately starts writing code without workflow
```

## 🔧 **PROJECT-SPECIFIC PATTERNS TO ALWAYS SEARCH:**

- **Manager Patterns**: `BaseManager`, `PopupController`
- **Storage Patterns**: `chrome.storage.local`, `chrome.storage.sync`
- **UI Patterns**: `showSection`, `navigateToSection`
- **Event Patterns**: `addEventListenerTracked`
- **API Patterns**: `performAnalysis`, `fetch`
- **Error Patterns**: `handleError`, `showError`

## 📊 **QUALITY ASSURANCE:**

Every code modification must:
1. **Follow existing patterns** found via MCP search
2. **Integrate properly** with found architecture
3. **Use consistent naming** with discovered conventions
4. **Respect existing storage keys** and data structures
5. **Maintain error handling patterns** found in codebase

---

**Remember: Context First, Set Path, Search Deep, Code Last. Always.**
