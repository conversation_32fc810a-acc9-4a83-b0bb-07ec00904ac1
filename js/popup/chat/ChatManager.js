/**
 * Chat Manager
 * Handles chat window creation, lifecycle, and communication
 */
import { BaseManager } from '../core/BaseManager.js';

export class ChatManager extends BaseManager {
    constructor(controller) {
        super(controller);
        this.chatWindowId = null;
        this.isWindowOpen = false;
        this.messageQueue = [];
        this.windowCloseListener = null;
    }

    async init() {
        await super.init();
        this.setupWindowCloseListener();
        this.setupMessageListener();
        console.log('ChatManager initialized');
    }

    /**
     * Setup window close listener
     */
    setupWindowCloseListener() {
        this.windowCloseListener = (windowId) => {
            this.handleWindowClosed(windowId);
        };
        
        if (chrome.windows && chrome.windows.onRemoved) {
            chrome.windows.onRemoved.addListener(this.windowCloseListener);
        }
    }

    /**
     * Open chat window
     */
    async openChatWindow() {
        try {
            // Check if window already exists and focus it
            if (this.isWindowOpen && this.chatWindowId) {
                try {
                    const window = await chrome.windows.get(this.chatWindowId);
                    if (window) {
                        await chrome.windows.update(this.chatWindowId, { focused: true });
                        console.log('Chat window focused');
                        return;
                    }
                } catch (error) {
                    // Window no longer exists, reset state
                    this.isWindowOpen = false;
                    this.chatWindowId = null;
                }
            }

            // Create new chat window
            const chatUrl = chrome.runtime.getURL('chat.html');
            console.log('Opening chat window with URL:', chatUrl);

            const window = await chrome.windows.create({
                url: chatUrl,
                type: 'popup',
                width: 1200,
                height: 700,
                focused: true,
                left: Math.round((screen.width - 1200) / 2),
                top: Math.round((screen.height - 700) / 2)
            });

            this.chatWindowId = window.id;
            this.isWindowOpen = true;
            
            this.handleSuccess('Chat window opened successfully');
            console.log('Chat window created with ID:', this.chatWindowId);

        } catch (error) {
            this.handleError(error, 'Failed to open chat window');
        }
    }

    /**
     * Close chat window
     */
    async closeChatWindow() {
        try {
            if (this.chatWindowId) {
                await chrome.windows.remove(this.chatWindowId);
                this.chatWindowId = null;
                this.isWindowOpen = false;
                console.log('Chat window closed');
            }
        } catch (error) {
            this.handleError(error, 'Failed to close chat window');
        }
    }

    /**
     * Handle window closed event
     */
    handleWindowClosed(windowId) {
        if (windowId === this.chatWindowId) {
            console.log('Chat window closed via close button');
            this.chatWindowId = null;
            this.isWindowOpen = false;
            this.messageQueue = []; // Clear any pending messages
        }
    }

    /**
     * Send message to Agent Hustle AI and get response
     */
    async sendMessageToAI(message) {
        try {
            // Get API key from settings
            const apiKey = await this.getApiKey();
            if (!apiKey) {
                throw new Error('No API key found. Please configure your API key in settings.');
            }

            // Send message to Agent Hustle AI service
            const response = await this.makeApiRequest(message, apiKey);
            
            // Send response back to chat window
            await this.sendMessageToChat({
                type: 'AI_RESPONSE',
                data: response
            });

            return response;
        } catch (error) {
            this.handleError(error, 'Failed to send message to AI service');
            
            // Send error to chat window
            await this.sendMessageToChat({
                type: 'AI_ERROR',
                data: error.message
            });
            
            throw error;
        }
    }

    /**
     * Make API request to Agent Hustle service
     */
    async makeApiRequest(message, apiKey) {
        const API_ENDPOINT = 'https://agenthustle.ai/api/chat';
        
        console.log('📡 ChatManager: Making API request to:', API_ENDPOINT);
        
        const response = await fetch(API_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-API-Key': apiKey
            },
            body: JSON.stringify({
                messages: [{ role: 'user', content: message }],
                vaultId: 'default'
            })
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error('API Error Response:', errorText);
            throw new Error(`API request failed: ${response.status} - ${errorText}`);
        }

        const responseText = await response.text();
        console.log('Response text length:', responseText.length);
        
        // Parse the response (same logic as background.js)
        let analysisResult;
        try {
            const lines = responseText.split('\n');
            let content = '';
            let hasContentStream = false;

            for (const line of lines) {
                if (line.startsWith('0:')) {
                    hasContentStream = true;
                    try {
                        const chunk = JSON.parse(line.substring(2));
                        content += chunk;
                    } catch (e) {
                        console.warn('Could not parse stream chunk:', line);
                    }
                }
            }

            if (hasContentStream) {
                analysisResult = { content: content };
                console.log('Parsed streamed content, length:', content.length);
            } else {
                analysisResult = JSON.parse(responseText);
                console.log('Parsed JSON response');
            }
        } catch (error) {
            console.error('Failed to parse response:', error);
            throw new Error(`Invalid response from server: ${error.message}`);
        }

        return analysisResult;
    }

    /**
     * Get API key from storage
     */
    async getApiKey() {
        try {
            const result = await chrome.storage.sync.get(['hustleApiKey']);
            return result.hustleApiKey;
        } catch (error) {
            console.error('Failed to get API key:', error);
            return null;
        }
    }

    /**
     * Send message to chat window
     */
    async sendMessageToChat(message) {
        if (!this.isWindowOpen || !this.chatWindowId) {
            console.warn('Chat window not open, queueing message');
            this.messageQueue.push(message);
            return;
        }

        try {
            // Send message to chat window via chrome.tabs.sendMessage
            const tabs = await chrome.tabs.query({ windowId: this.chatWindowId });
            if (tabs.length > 0) {
                await chrome.tabs.sendMessage(tabs[0].id, message);
            }
        } catch (error) {
            this.handleError(error, 'Failed to send message to chat window');
        }
    }

    /**
     * Handle communication from chat window
     */
    handleWindowCommunication(message) {
        console.log('Received message from chat window:', message);
        
        switch (message.type) {
            case 'CHAT_READY':
                this.handleChatReady();
                break;
            case 'CHAT_MESSAGE':
                this.handleChatMessage(message.data);
                break;
            case 'WINDOW_CLOSING':
                this.handleWindowClosing();
                break;
            default:
                console.warn('Unknown message type from chat window:', message.type);
        }
    }

    /**
     * Setup message listener for chat window communication
     */
    setupMessageListener() {
        if (typeof window !== 'undefined') {
            window.addEventListener('message', (event) => {
                // Validate message origin and source
                if (event.data && event.data.source === 'agent-hustle-chat') {
                    this.handleWindowCommunication(event.data);
                }
            });
        }
    }

    /**
     * Handle chat ready event
     */
    handleChatReady() {
        console.log('Chat window ready, processing queued messages');
        
        // Process any queued messages
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.sendMessageToChat(message);
        }
    }

    /**
     * Handle chat message from chat window
     */
    async handleChatMessage(data) {
        console.log('Processing chat message:', data);
        
        try {
            // Send message to AI service and get response
            const response = await this.sendMessageToAI(data);
            console.log('AI response received:', response);
        } catch (error) {
            console.error('Failed to process chat message:', error);
        }
    }

    /**
     * Handle window closing
     */
    handleWindowClosing() {
        console.log('Chat window is closing');
        this.chatWindowId = null;
        this.isWindowOpen = false;
        this.messageQueue = [];
    }

    /**
     * Check if chat window is open
     */
    isChatWindowOpen() {
        return this.isWindowOpen && this.chatWindowId !== null;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        if (this.windowCloseListener && chrome.windows && chrome.windows.onRemoved) {
            chrome.windows.onRemoved.removeListener(this.windowCloseListener);
        }
        
        if (this.isChatWindowOpen()) {
            this.closeChatWindow();
        }
        
        this.chatWindowId = null;
        this.isWindowOpen = false;
        this.messageQueue = [];
        
        super.cleanup();
    }
}